import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { of } from 'rxjs';

import { CaseService, LetterRecord } from '../../services/case.service';
import { TemplateService, TemplatePackage } from '../../services/template.service';
import { Case, PartyType } from '../../interfaces/case.interface';
import { environment } from '../../../environments/environment';
import { validateCase, validateParty } from '../../utils/data-validation.utils';
import { getUserDisplayName } from '../../utils/user.utils';

@Component({
  selector: 'app-case-export',
  templateUrl: './case-export.component.html',
  styleUrls: ['./case-export.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatChipsModule,
    MatInputModule
  ]
})
export class CaseExportComponent implements OnInit {
  case: Case | null = null;
  templates: TemplatePackage[] = [];
  selectedTemplateId: number | null = null;
  isExporting = false;
  isLoading = true;
  error: string | null = null;

  // 表格相关
  displayedColumns: string[] = ['key', 'value'];
  replacementData: { key: string; value: string }[] = [];

  // 函件相关
  letterRecords: LetterRecord[] = [];
  selectedLetterIds: number[] = [];
  isLoadingLetters = false;

  // 搜索相关
  letterSearchControl = new FormControl();
  filteredLetterRecords: LetterRecord[] = [];
  selectedLetters: LetterRecord[] = [];

  // 调试信息
  debugInfo: string = '';

  // 导出工具函数供模板使用
  getUserDisplayName = getUserDisplayName;

  constructor(
    private route: ActivatedRoute,
    private caseService: CaseService,
    private templateService: TemplateService,
    private snackBar: MatSnackBar,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadCase(+id);
    }
    this.loadTemplates();
    this.loadRecentLetterRecords();

    // 监听搜索输入变化
    this.letterSearchControl.valueChanges.subscribe(value => {
      this.filterLetterRecords(value);
    });
  }

  loadCase(id: number): void {
    this.isLoading = true;
    this.caseService.getCase(id).subscribe({
      next: (caseData) => {
        if (!validateCase(caseData)) {
          this.error = '案件数据格式不正确或缺少必要信息';
          this.snackBar.open('案件数据验证失败，可能影响导出结果', '关闭', {
            duration: 5000,
          });
        }
        this.case = caseData;
        this.isLoading = false;
        this.updateReplacementData();
      },
      error: (error) => {
        this.error = '加载案件详情失败';
        this.isLoading = false;
        console.error('加载案件详情出错:', error);
      }
    });
  }

  loadTemplates(): void {
    this.templateService.getTemplates().subscribe({
      next: (templates) => {
        this.templates = templates;
      },
      error: (error) => {
        console.error('加载模板列表失败:', error);
        this.snackBar.open('加载模板列表失败', '关闭', {
          duration: 3000,
        });
      }
    });
  }

  // 获取状态的中文显示
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'CREATED': '创建',
      'APPLYING': '申请中',
      'ADMIN_REVIEWING': '行政审批中',
      'DIRECTOR_REVIEWING': '主任审批中',
      'PROCESSING_DELEGATION': '办理委托手续中',
      'IN_PROGRESS': '办案中',
      'APPLYING_CLOSURE': '申请结案中',
      'CLOSURE_APPROVED': '已通过主任审批结案',
      'ARCHIVED': '已归档'
    };
    return statusMap[status] || status;
  }

  // 获取委托人名称
  private getClientName(): string {
    if (!this.case || !this.case.parties || this.case.parties.length === 0) {
      this.debugInfo = '调试信息：案件或当事人信息为空';
      return '';
    }

    this.debugInfo = `调试信息：当事人总数 ${this.case.parties.length}`;

    // 查找标记为is_client=true的当事人作为委托人
    const clientParties = this.case.parties.filter(party => party.is_client);

    if (clientParties.length === 0) {
      this.debugInfo += `\n未找到委托人。当事人is_client状态：${this.case.parties.map(p => `ID:${p.id},is_client:${p.is_client}`).join('; ')}`;
      return '';
    }

    // 使用第一个标记为委托人的当事人
    const client = clientParties[0];
    this.debugInfo += `\n找到委托人，ID: ${client.id}`;

    if (client.natural_person) {
      const name = client.natural_person.name || '';
      this.debugInfo += `\n委托人为自然人，姓名: '${name}'`;
      return name;
    } else if (client.legal_entity) {
      const name = client.legal_entity.name || '';
      this.debugInfo += `\n委托人为法人实体，名称: '${name}'`;
      return name;
    } else {
      this.debugInfo += '\n委托人既没有自然人信息也没有法人信息';
      return '';
    }
  }

  // 计算对方当事人的值
  private getOpposingParties(): string {
    if (!this.case || !this.case.parties || this.case.parties.length === 0) {
      return '';
    }

    // 只有民事案件、行政案件和其他案件才有对方当事人
    if (this.case.content && this.case.content['案件类型']) {
      const caseType = this.case.content['案件类型'];
      if (!['民事案件', '行政案件', '其他案件'].includes(caseType)) {
        return '';
      }
    } else {
      return '';
    }

    // 找出所有委托人
    const clientParties = this.case.parties.filter(party => party.is_client);

    // 如果没有委托人，返回空字符串
    if (clientParties.length === 0) {
      return '';
    }

    // 获取我方当事人类型（假设所有委托人角色相同）
    const clientType = clientParties[0].party_type;

    let opposingParties: string[] = [];

    if (clientType === PartyType.PLAINTIFF) {
      // 如果我方是原告，对方是被告和第三人
      opposingParties = this.case.parties
        .filter(party => party.party_type === PartyType.DEFENDANT || party.party_type === PartyType.THIRD_PARTY)
        .map(party => {
          if (party.natural_person) {
            return party.natural_person.name;
          } else if (party.legal_entity) {
            return party.legal_entity.name;
          }
          return '';
        })
        .filter(name => name); // 过滤掉空名称
    } else if (clientType === PartyType.DEFENDANT || clientType === PartyType.THIRD_PARTY) {
      // 如果我方是被告或第三人，对方是原告
      opposingParties = this.case.parties
        .filter(party => party.party_type === PartyType.PLAINTIFF)
        .map(party => {
          if (party.natural_person) {
            return party.natural_person.name;
          } else if (party.legal_entity) {
            return party.legal_entity.name;
          }
          return '';
        })
        .filter(name => name); // 过滤掉空名称
    }

    // 用顿号连接对方当事人名称
    return opposingParties.join('、');
  }

  // 获取犯罪嫌疑人名称
  private getCriminalSuspect(): string {
    if (!this.case || !this.case.parties || this.case.parties.length === 0) {
      return '';
    }

    // 只有刑事案件才有犯罪嫌疑人
    if (this.case.content && this.case.content['案件类型']) {
      const caseType = this.case.content['案件类型'];
      if (caseType !== '刑事案件') {
        return '';
      }
    } else {
      return '';
    }

    // 查找角色为犯罪嫌疑人的当事人
    const suspectParties = this.case.parties.filter(party => party.party_type === PartyType.SUSPECT);

    if (suspectParties.length === 0) {
      return '';
    }

    // 获取所有犯罪嫌疑人的名称
    const suspectNames = suspectParties
      .map(party => {
        if (party.natural_person) {
          return party.natural_person.name;
        } else if (party.legal_entity) {
          return party.legal_entity.name;
        }
        return '';
      })
      .filter(name => name); // 过滤掉空名称

    // 用顿号连接犯罪嫌疑人名称
    return suspectNames.join('、');
  }

  // 获取协助律师名称
  private getCollaboratingLawyers(): string {
    if (!this.case?.collaborating_lawyers || this.case.collaborating_lawyers.length === 0) {
      return '';
    }

    // 提取协助律师姓名
    const lawyerNames = this.case.collaborating_lawyers.map(lawyer => this.getUserDisplayName(lawyer));

    // 用顿号连接协助律师姓名
    return lawyerNames.join('、');
  }

  updateReplacementData(): void {
    if (!this.case) return;

    const currentDate = new Date();
    const replacements: { key: string; value: string }[] = [
      { key: '{{ 年 }}', value: currentDate.getFullYear().toString() + '年' },
      { key: '{{ 月 }}', value: (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月' },
      { key: '{{ 日 }}', value: currentDate.getDate().toString().padStart(2, '0') + '日' },
      { key: '{{ 案件编号 }}', value: this.case.case_number || '' },
      { key: '{{ 案由 }}', value: this.case.case_cause || '' },
      { key: '{{ 承办律师 }}', value: this.getUserDisplayName(this.case.lawyer) },
      { key: '{{ 协助律师 }}', value: this.getCollaboratingLawyers() },
      { key: '{{ 案件状态 }}', value: this.getStatusText(this.case.status) },
      { key: '{{ 创建时间 }}', value: this.case.created_at ? new Date(this.case.created_at).toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '年').replace(/\//g, '月') + '日' : '' },
      { key: '{{ 更新时间 }}', value: this.case.updated_at ? new Date(this.case.updated_at).toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '年').replace(/\//g, '月') + '日' : '' },
      { key: '{{ 缴费状态 }}', value: this.case.is_paid ? '已缴费' : '未缴费' },
      { key: '{{ 商定律师费 }}', value: this.case.agreed_lawyer_fee ? this.case.agreed_lawyer_fee.toString() : '' },
      { key: '{{ 委托人 }}', value: this.getClientName() },
      { key: '{{ 对方当事人 }}', value: this.getOpposingParties() },
      { key: '{{ 犯罪嫌疑人 }}', value: this.getCriminalSuspect() },
      { key: '{{ 函件编号 }}', value: this.getSelectedLetterNumbers() }
    ];

    // 添加案件内容字段
    if (this.case.content) {
      Object.entries(this.case.content).forEach(([key, value]) => {
        // 避免重复添加已经手动添加的字段
        if (key !== '对方当事人') {
        replacements.push({
          key: `{{ ${key} }}`,
          value: value?.toString() || ''
        });
        }
      });
    }

    this.replacementData = replacements;

    // 触发委托人信息计算以更新调试信息
    this.getClientName();
  }

  // 当函件选择发生变化时，更新替换数据
  onLetterSelectionChange(): void {
    this.updateReplacementData();
  }

  exportDocument(): void {
    if (!this.case || !this.selectedTemplateId) {
      this.snackBar.open('请选择要导出的模板', '关闭', {
        duration: 3000,
      });
      return;
    }

    this.isExporting = true;

    // 构建替换参数
    const replacements: Record<string, string> = {};
    this.replacementData.forEach(item => {
      // 移除 {{ 和 }} 来获取实际的键名
      const key = item.key.replace(/[{}\s]/g, '');
      // 确保即使值为空也进行替换，null 或 undefined 转换为空字符串
      replacements[key] = item.value ?? '';
    });

    this.templateService.generateFiles(this.selectedTemplateId, replacements).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.case!.case_number}_导出文档.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        this.isExporting = false;
        this.snackBar.open('文档导出成功', '关闭', {
          duration: 3000,
        });
      },
      error: (error) => {
        console.error('导出文档失败:', error);
        this.isExporting = false;
        this.snackBar.open('导出文档失败', '关闭', {
          duration: 3000,
        });
      }
    });
  }

  // 加载近一个月的函件记录
  loadRecentLetterRecords(): void {
    this.isLoadingLetters = true;

    // 计算一个月前的日期
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);

    const params = {
      start_date: this.formatDate(startDate),
      end_date: this.formatDate(endDate)
    };

    this.caseService.getLetterRecords(params).subscribe({
      next: (records) => {
        this.letterRecords = records;
        this.filteredLetterRecords = records;
        this.isLoadingLetters = false;
      },
      error: (error) => {
        console.error('加载函件记录失败:', error);
        this.snackBar.open('加载函件记录失败', '关闭', {
          duration: 3000,
        });
        this.isLoadingLetters = false;
      }
    });
  }

  // 过滤函件记录
  filterLetterRecords(searchValue: string): void {
    if (!searchValue || searchValue.trim() === '') {
      this.filteredLetterRecords = this.letterRecords;
      return;
    }

    const searchTerm = searchValue.toLowerCase();
    this.filteredLetterRecords = this.letterRecords.filter(letter => {
      // 搜索函件显示文本（包含编号和委托人）
      const displayText = this.getLetterDisplayText(letter).toLowerCase();

      // 搜索委托人名字
      const clientName = (letter.client_name || '').toLowerCase();

      // 搜索案件编号
      const caseNumber = (letter.case_number || '').toLowerCase();

      // 搜索致函单位
      const recipientUnit = (letter.recipient_unit || '').toLowerCase();

      // 搜索使用律师
      const lawyerName = (letter.lawyer_name || '').toLowerCase();

      // 搜索函件类型
      const letterType = (letter.letter_type || '').toLowerCase();

      // 搜索备注
      const remarks = (letter.remarks || '').toLowerCase();

      // 在所有字段中搜索
      return displayText.includes(searchTerm) ||
             clientName.includes(searchTerm) ||
             caseNumber.includes(searchTerm) ||
             recipientUnit.includes(searchTerm) ||
             lawyerName.includes(searchTerm) ||
             letterType.includes(searchTerm) ||
             remarks.includes(searchTerm);
    });
  }

  // 选择函件
  selectLetter(letter: LetterRecord): void {
    if (letter.id && !this.selectedLetterIds.includes(letter.id)) {
      this.selectedLetterIds.push(letter.id);
      this.selectedLetters.push(letter);
      this.onLetterSelectionChange();
    }
    // 清空搜索框
    this.letterSearchControl.setValue('');
  }

  // 移除选中的函件
  removeLetter(letter: LetterRecord): void {
    if (letter.id) {
      const idIndex = this.selectedLetterIds.indexOf(letter.id);
      const letterIndex = this.selectedLetters.findIndex(l => l.id === letter.id);

      if (idIndex >= 0) {
        this.selectedLetterIds.splice(idIndex, 1);
      }
      if (letterIndex >= 0) {
        this.selectedLetters.splice(letterIndex, 1);
      }

      this.onLetterSelectionChange();
    }
  }

  // 格式化函件显示文本
  getLetterDisplayText(letter: LetterRecord): string {
    const letterNumber = letter.letter_number ?
      `（${letter.year}）${letter.letter_type}${letter.letter_number}号` :
      '未生成编号';
    return `${letterNumber} - ${letter.client_name}`;
  }

  // 获取选中的函件编号列表
  getSelectedLetterNumbers(): string {
    if (this.selectedLetterIds.length === 0) {
      return '';
    }

    const selectedLetters = this.letterRecords.filter(letter =>
      this.selectedLetterIds.includes(letter.id!)
    );

    return selectedLetters.map(letter => {
      return letter.letter_number ?
        `（${letter.year}）${letter.letter_type}${letter.letter_number}号` :
        '未生成编号';
    }).join('、');
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

}
